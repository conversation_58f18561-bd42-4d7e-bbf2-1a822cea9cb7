import org.gradle.api.tasks.testing.logging.TestExceptionFormat
import java.net.InetAddress

plugins {
    alias(libs.plugins.spring.boot)
    alias(libs.plugins.jib)
    alias(libs.plugins.springdoc.openapi)
    alias(libs.plugins.openapi)
    alias(libs.plugins.git.properties)
}

dependencies {
    implementation(project(":spring-app-template-api"))

    // Spring
    implementation(libs.spring.boot.starter.web)
    implementation(libs.swagger.annotations)
    implementation(libs.spring.boot.starter.webflux)
    implementation(libs.spring.boot.starter.data.jpa)
    implementation(libs.spring.boot.starter.validation)
    implementation(libs.spring.boot.starter.actuator)
    implementation(libs.spring.cloud.starter.openfeign)

    // SpringDoc OpenAPI UI
    implementation(libs.springdoc.openapi)
    implementation(libs.webjars.locator.core)
    implementation(libs.spring.webmvc)
    implementation(libs.webjars.swagger.ui)

    // Database
    implementation(libs.liquibase)
    implementation(libs.postgresql)

    // Utils
    implementation(libs.mapstruct)
    implementation(libs.logformat.starter)
    implementation(libs.sentry.starter)
    implementation(libs.jackson.dataformat.xml)

    // Monitoring
    runtimeOnly(libs.micrometer.prometheus)

    // Testing
    testImplementation(libs.spring.boot.starter.test)
    testImplementation(libs.testcontainers.junit)
    testImplementation(libs.testcontainers.postgresql)
    testImplementation(libs.hamcrest)
    testImplementation(libs.mockito)
    testImplementation(libs.webjars.locator.core)
    testImplementation(libs.spring.webmvc)

    // Annotation Processors
    annotationProcessor(libs.mapstruct.processor)
}

springBoot {
    buildInfo {
        properties {
            additional.put(
                "created-by",
                "${System.getProperty("java.version")} (${System.getProperty("java.vendor")} ${System.getProperty("java.vm.version")})",
            )
            additional.put("built-by", "${System.getProperty("user.name")}@${InetAddress.getLocalHost().hostName}")
            additional.put("built-os", System.getProperty("os.name"))
            additional.put(
                "build-revision",
                providers.provider {
                    project.extensions.extraProperties.get("gitProps")?.let { props ->
                        (props as Map<*, *>)["git.commit.id.abbrev"]?.toString()
                    } ?: "unknown"
                },
            )
            additional.put(
                "build-branch",
                providers.provider {
                    project.extensions.extraProperties.get("gitProps")?.let { props ->
                        (props as Map<*, *>)["git.branch"]?.toString()
                    } ?: "unknown"
                },
            )
        }
    }
}

gitProperties {
    extProperty = "gitProps"
    keys = listOf("git.branch", "git.commit.id.abbrev")
    dateFormat = "yyyy-MM-dd'T'HH:mm:ssZ"
    dateFormatTimeZone = "UTC"
    dotGitDirectory.set(rootProject.file(".git"))
}

tasks.generateGitProperties {
    outputs.upToDateWhen { false }
}

tasks.named("bootBuildInfo") {
    dependsOn("generateGitProperties")
}

tasks.withType<Test> {
    group = "verification"
    useJUnitPlatform()
    testLogging {
        showStandardStreams = true
        events("passed", "skipped", "failed")
        exceptionFormat = TestExceptionFormat.FULL
    }
}

tasks.test {
    useJUnitPlatform {
        includeTags("unit")
    }
}

tasks.register<Test>("it") {
    group = "verification"
    description = "Runs integration tests."

    testClassesDirs = sourceSets.test.get().output.classesDirs
    classpath = sourceSets.test.get().runtimeClasspath

    useJUnitPlatform {
        includeTags("integration")
    }

    if (System.getProperty("spring.profiles.active") == "it") {
        systemProperty("spring.profiles.active", "it")
    }
}

tasks.withType<Test> {
    systemProperty("mockito.mock-maker-inline", "true")
    jvmArgs(
        "-XX:+EnableDynamicAgentLoading",
        "-Djdk.instrument.traceUsage=false",
        "-Xshare:off",
    )
}

tasks.check {
    dependsOn("test", "it")
}

jib {
    from {
        image = "bellsoft/liberica-openjdk-debian:21.0.2"
    }
    container {
        mainClass = "uz.uzum.springapptemplate.SpringAppTemplateApplication"
    }
}

openApi {
    apiDocsUrl.set("http://localhost:8020/v3/api-docs")
    outputDir.set(file("build/swagger"))
    outputFileName.set("swagger.yaml")
    customBootRun {
        val props = System.getProperties().map { (k, v) -> k.toString() to v.toString() }.toMap()
        systemProperties.putAll(props)
        jvmArgs =
            listOf(
                "-Dserver.port=8020",
                "-Dspring.liquibase.enabled=false",
            )
    }
}

dependencies {
    implementation(project(":spring-app-template-api"))
}

tasks.named("openApiGenerate") {
    enabled = false
}

tasks.generateOpenApiDocs {
    outputs.upToDateWhen { false }
}

tasks.named("forkedSpringBootRun") {
    mustRunAfter(":spring-app-template-api:jar")
}

tasks.jar {
    enabled = false
}
