package uz.uzum.springapptemplate.unit;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.Test;
import uz.uzum.springapptemplate.unit.config.UnitTest;

@UnitTest
public class SpringAppTemplateTest {

    @Test
    public void shouldReturnExpectedValue() {
        String expected = "Hello from template!";
        String actual = "Hello from template!";
        assertEquals(expected, actual);
    }

    @Test
    public void shouldRunInParallel() {
        assertTrue(true, "Тест запущен параллельно");

        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
}
