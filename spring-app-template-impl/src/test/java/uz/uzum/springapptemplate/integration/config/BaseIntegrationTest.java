package uz.uzum.springapptemplate.integration.config;

import java.time.Duration;
import java.util.Arrays;
import javax.sql.DataSource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.AutoConfigureWebTestClient;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ActiveProfilesResolver;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.support.TestPropertySourceUtils;
import org.springframework.test.web.reactive.server.WebTestClient;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.containers.wait.strategy.Wait;
import org.testcontainers.utility.DockerImageName;
import uz.uzum.springapptemplate.SpringAppTemplateApplication;

@SpringBootTest(
        webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
        classes = {SpringAppTemplateApplication.class})
@ActiveProfiles(resolver = BaseIntegrationTest.ProfileResolver.class)
@DirtiesContext
@ContextConfiguration(initializers = {BaseIntegrationTest.Initializer.class})
@AutoConfigureWebTestClient(timeout = "PT30S")
@Tag("integration")
@Slf4j
public abstract class BaseIntegrationTest {

    @Autowired
    protected ApplicationContext applicationContext;

    @Autowired
    protected Environment environment;

    @Autowired
    protected DataSource dataSource;

    @Autowired
    protected JdbcTemplate jdbcTemplate;

    @Autowired
    protected WebTestClient webTestClient;

    private static final PostgreSQLContainer<?> POSTGRESQL_CONTAINER;

    static {
        String activeProfiles = System.getProperty("spring.profiles.active", "");
        boolean isCI = activeProfiles.contains("it");

        if (isCI) {
            POSTGRESQL_CONTAINER = null;
            log.info("CI environment detected (profile 'it' active). TestContainers disabled.");
        } else {
            POSTGRESQL_CONTAINER = new PostgreSQLContainer<>(DockerImageName.parse("postgres:15-alpine"))
                    .withDatabaseName("integration_tests_db")
                    .withUsername("test")
                    .withPassword("test")
                    .withStartupTimeout(Duration.ofMinutes(5))
                    .waitingFor(Wait.forLogMessage(".*database system is ready to accept connections.*\\s", 2));
            log.info("Local environment detected. TestContainers enabled.");
        }
    }

    @BeforeAll
    static void setup() {
        if (POSTGRESQL_CONTAINER != null) {
            POSTGRESQL_CONTAINER.start();
            log.info("PostgreSQL TestContainer started: {}", POSTGRESQL_CONTAINER.getJdbcUrl());
        } else {
            log.info("TestContainers disabled - using external database configuration");
        }
    }

    static class ProfileResolver implements ActiveProfilesResolver {

        @Override
        public String[] resolve(Class<?> testClass) {
            String activeProfiles = System.getProperty("spring.profiles.active", "");
            boolean isCI = activeProfiles.contains("it");

            if (isCI) {
                return new String[] {"test", "it"};
            } else {
                return new String[] {"test", "testcontainers"};
            }
        }
    }

    static class Initializer implements ApplicationContextInitializer<ConfigurableApplicationContext> {

        @Override
        public void initialize(ConfigurableApplicationContext configurableApplicationContext) {
            String[] activeProfiles =
                    configurableApplicationContext.getEnvironment().getActiveProfiles();
            boolean isCI = Arrays.asList(activeProfiles).contains("it");

            if (isCI) {
                TestPropertySourceUtils.addInlinedPropertiesToEnvironment(
                        configurableApplicationContext,
                        "spring.datasource.url="
                                + System.getProperty("DB_URL", "****************************************"),
                        "spring.datasource.username=" + System.getProperty("POSTGRES_USER", "postgres"),
                        "spring.datasource.password=" + System.getProperty("POSTGRES_PASSWORD", "postgres"),
                        "spring.jpa.hibernate.ddl-auto=create-drop",
                        "spring.liquibase.enabled=true");
                log.info("CI environment properties applied to application context");
            } else if (POSTGRESQL_CONTAINER != null) {
                TestPropertySourceUtils.addInlinedPropertiesToEnvironment(
                        configurableApplicationContext,
                        "spring.datasource.url=" + POSTGRESQL_CONTAINER.getJdbcUrl(),
                        "spring.datasource.username=" + POSTGRESQL_CONTAINER.getUsername(),
                        "spring.datasource.password=" + POSTGRESQL_CONTAINER.getPassword(),
                        "spring.jpa.hibernate.ddl-auto=create-drop",
                        "spring.liquibase.enabled=true");
                log.info("TestContainers properties applied to application context");
            } else {
                log.warn("No database configuration applied - neither CI nor TestContainers available");
            }
        }
    }

    protected ApplicationContext getApplicationContext() {
        return applicationContext;
    }

    protected Environment getEnvironment() {
        return environment;
    }

    protected DataSource getDataSource() {
        return dataSource;
    }

    protected JdbcTemplate getJdbcTemplate() {
        return jdbcTemplate;
    }

    protected WebTestClient getWebTestClient() {
        return webTestClient;
    }

    protected String getContextPath() {
        return environment.getProperty("server.servlet.context-path", "/api");
    }

    protected <T> T getBean(Class<T> beanClass) {
        return applicationContext.getBean(beanClass);
    }

    protected <T> T getBean(String name, Class<T> beanClass) {
        return applicationContext.getBean(name, beanClass);
    }

    protected WebTestClient.ResponseSpec performGet(String path) {
        return webTestClient.get().uri(getContextPath() + path).exchange();
    }

    protected WebTestClient.ResponseSpec performPost(String path, Object requestBody) {
        return webTestClient
                .post()
                .uri(getContextPath() + path)
                .bodyValue(requestBody)
                .exchange();
    }

    protected WebTestClient.ResponseSpec performPut(String path, Object requestBody) {
        return webTestClient
                .put()
                .uri(getContextPath() + path)
                .bodyValue(requestBody)
                .exchange();
    }

    protected WebTestClient.ResponseSpec performDelete(String path) {
        return webTestClient.delete().uri(getContextPath() + path).exchange();
    }
}
