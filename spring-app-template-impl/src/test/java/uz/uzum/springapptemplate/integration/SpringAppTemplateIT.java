package uz.uzum.springapptemplate.integration;

import static org.junit.jupiter.api.Assertions.assertNotNull;

import javax.sql.DataSource;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.ApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.web.reactive.server.WebTestClient;
import uz.uzum.springapptemplate.integration.config.BaseIntegrationTest;

public class SpringAppTemplateIT extends BaseIntegrationTest {

    @Test
    void contextLoads() {
        assertNotNull(getApplicationContext(), "Контекст приложения должен быть загружен");
    }

    @Test
    void shouldAccessJdbcTemplate() {
        JdbcTemplate jdbcTemplate = getJdbcTemplate();
        assertNotNull(jdbcTemplate, "JdbcTemplate должен быть доступен");

        Integer result = jdbcTemplate.queryForObject("SELECT 1", Integer.class);
        assertNotNull(result, "Должен вернуть результат запроса");
    }

    @Test
    void shouldAccessEnvironment() {
        Environment environment = getEnvironment();
        assertNotNull(environment, "Environment должен быть доступен");

        String activeProfiles = String.join(", ", environment.getActiveProfiles());
        assertNotNull(activeProfiles, "Должны быть активные профили");
    }

    @Test
    void shouldAccessDataSource() {
        DataSource dataSource = getDataSource();
        assertNotNull(dataSource, "DataSource должен быть доступен");
    }

    @Test
    void shouldAccessApplicationContext() {
        ApplicationContext applicationContext = getApplicationContext();
        assertNotNull(applicationContext, "ApplicationContext должен быть доступен");

        Environment env = getBean(Environment.class);
        assertNotNull(env, "Должен вернуть бин Environment");
    }

    @Test
    @Disabled("todo добавить тест эндпоинат через wiremock")
    void shouldAccessWebTestClient() {
        try {
            WebTestClient webTestClient = getWebTestClient();
            assertNotNull(webTestClient, "WebTestClient должен быть доступен");

            String contextPath = getContextPath();
            assertNotNull(contextPath, "contextPath должен быть доступен");

            performGet("/health").expectStatus().isOk();

            performPost("/example", new ExampleRequest("test")).expectStatus().isOk();
        } catch (IllegalStateException e) {
            System.out.println("WebTestClient не доступен: " + e.getMessage());
        }
    }

    private record ExampleRequest(String value) {}
}
