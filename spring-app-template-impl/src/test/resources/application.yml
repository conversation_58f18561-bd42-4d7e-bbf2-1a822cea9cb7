---
spring:
  profiles:
    active: "testcontainers"
  application:
    name: "spring-app-template"
  jpa:
    open-in-view: false
    properties:
      hibernate:
        dialect: "org.hibernate.dialect.PostgreSQLDialect"
    database-platform: "org.hibernate.dialect.PostgreSQLDialect"
  liquibase:
    change-log: "classpath:db/changelog/db.changelog-master.yml"
    enabled: false
  jackson:
    time-zone: "UTC"
