databaseChangeLog:
  - changeSet:
      id: 20250315194508_spring_app_template_migration
      author: <PERSON><PERSON><PERSON>
      preConditions:
        - onFail: MARK_RAN
      changes:
        - createTable:
            tableName: spring_app_template_entity
            columns:
              - column:
                  name: id
                  type: uuid
                  constraints:
                    primaryKey: true
                    nullable: false
              - column:
                  name: name
                  type: varchar(255)
                  constraints:
                    nullable: false
              - column:
                  name: description
                  type: varchar(1000)
                  constraints:
                    nullable: true
              - column:
                  name: is_active
                  type: boolean
                  defaultValueBoolean: true
                  constraints:
                    nullable: false
              - column:
                  name: priority
                  type: int
                  constraints:
                    nullable: true
              - column:
                  name: created_at
                  type: timestamp
                  defaultValueComputed: CURRENT_TIMESTAMP
                  constraints:
                    nullable: false
              - column:
                  name: updated_at
                  type: timestamp
                  defaultValueComputed: CURRENT_TIMESTAMP
                  constraints:
                    nullable: false
        - createIndex:
            indexName: idx_spring_app_template_entity_name
            tableName: spring_app_template_entity
            columns:
              - column:
                  name: name
        - createIndex:
            indexName: idx_spring_app_template_entity_priority
            tableName: spring_app_template_entity
            columns:
              - column:
                  name: priority
        - createIndex:
            indexName: idx_spring_app_template_entity_is_active
            tableName: spring_app_template_entity
            columns:
              - column:
                  name: is_active
