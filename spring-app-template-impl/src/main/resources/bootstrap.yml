---
spring:
  application:
    name: "spring-app-template"
  cloud:
    vault:
      enabled: "${VAULT_ENABLED:false}"
      host: "${VAULT_HOST:localhost}"
      port: "${VAULT_PORT:8200}"
      scheme: "${VAULT_SCHEME:http}"
      authentication: "TOKEN"
      token: "${VAULT_TOKEN:}"
      connection-timeout: "${VAULT_CONNECTION_TIMEOUT:5000}"
      read-timeout: "${VAULT_READ_TIMEOUT:15000}"
      fail-fast: "${VAULT_FAIL_FAST:false}"
      config:
        order: -10
        lifecycle:
          enabled: true
          min-renewal: 10s
          expiry-threshold: 1m
          lease-endpoints: "SysLeases"
      generic:
        enabled: true
        backend: "secret"
        profile-separator: "/"
        default-context: "${spring.application.name}"
        application-name: "${spring.application.name}"
      database:
        enabled: "${VAULT_DATABASE_ENABLED:false}"
        role: "${VAULT_DATABASE_ROLE:}"
        backend: "database"
        username-property: "spring.datasource.username"
        password-property: "spring.datasource.password"
      kv:
        enabled: true
        backend: "secret"
        profile-separator: "/"
        default-context: "${spring.application.name}"
        application-name: "${spring.application.name}"
