---
spring:
  profiles:
    active: "${SPRING_PROFILES_ACTIVE:local}"
  application:
    name: "spring-app-template"
  jpa:
    open-in-view: false
    properties:
      hibernate:
        dialect: "org.hibernate.dialect.PostgreSQLDialect"
    database-platform: "org.hibernate.dialect.PostgreSQLDialect"
  datasource:
    url: "******************************************"
    username: "postgres"
    password: "postgres"
    driver-class-name: "org.postgresql.Driver"
  liquibase:
    change-log: "classpath:db/changelog/db.changelog-master.yml"
  jackson:
    time-zone: "UTC"
springdoc:
  swagger-ui:
    disable-swagger-default-url: true
    path: "/swagger-ui.html"
  api-docs:
    path: "/v3/api-docs"
    version: "openapi_3_0"
  show-actuator: false
management:
  endpoints:
    web:
      exposure:
        include: "health,info,prometheus"
  metrics:
    tags:
      application: "${spring.application.name}"
