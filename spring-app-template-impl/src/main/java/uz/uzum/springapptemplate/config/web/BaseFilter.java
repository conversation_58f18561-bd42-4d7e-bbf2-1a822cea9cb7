package uz.uzum.springapptemplate.config.web;

import jakarta.servlet.http.HttpServletRequest;
import java.util.HashSet;
import java.util.Set;
import org.springframework.web.filter.OncePerRequestFilter;

public abstract class BaseFilter extends OncePerRequestFilter {

    private static final Set<String> IGNORED_PATHS = new HashSet<>();

    static {
        IGNORED_PATHS.add("/swagger");
        IGNORED_PATHS.add("/api-docs");
        IGNORED_PATHS.add("/csrf");
        IGNORED_PATHS.add("/actuator");
        IGNORED_PATHS.add("/hystrix.stream");
        IGNORED_PATHS.add("/hystrix");
    }

    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) {
        String path = request.getRequestURI();
        return IGNORED_PATHS.stream().anyMatch(path::contains);
    }
}
