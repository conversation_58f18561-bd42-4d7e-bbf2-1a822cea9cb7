package uz.uzum.springapptemplate.config.feign;

import feign.Logger;
import feign.RequestInterceptor;
import io.opentelemetry.api.OpenTelemetry;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import uz.uzum.logformatter.config.OpenTelemetrySdkComponentsFactory;
import uz.uzum.logformatter.extractor.ContentExtractor;

@Configuration
@EnableFeignClients
public class BaseFeignConfig {

    @Bean
    public FeignLogger logger(ContentExtractor contentExtractor) {
        return new FeignLogger(contentExtractor);
    }

    @Bean
    Logger.Level feignLoggerLevel() {
        return Logger.Level.FULL;
    }

    @Bean("feignOpenTelemetryInterceptor")
    public RequestInterceptor feignOpenTelemetryInterceptor(OpenTelemetry openTelemetry) {
        return OpenTelemetrySdkComponentsFactory.createFeignOpentelemetryInterceptor(openTelemetry);
    }
}
