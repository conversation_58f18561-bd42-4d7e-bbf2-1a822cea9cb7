package uz.uzum.springapptemplate.mapper;

import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import uz.uzum.springapptemplate.dto.SpringAppTemplateDto;
import uz.uzum.springapptemplate.model.SpringAppTemplateEntity;

@Mapper(
        componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface SpringAppTemplateMapper {

    SpringAppTemplateDto toDto(SpringAppTemplateEntity entity);

    List<SpringAppTemplateDto> toDtoList(List<SpringAppTemplateEntity> entities);

    SpringAppTemplateEntity toEntity(SpringAppTemplateDto dto);

    void updateEntityFromDto(SpringAppTemplateDto dto, @MappingTarget SpringAppTemplateEntity entity);
}
