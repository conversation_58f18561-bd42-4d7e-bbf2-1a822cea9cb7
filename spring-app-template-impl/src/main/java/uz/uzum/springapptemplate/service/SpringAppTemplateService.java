package uz.uzum.springapptemplate.service;

import jakarta.persistence.EntityNotFoundException;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import uz.uzum.springapptemplate.dto.SpringAppTemplateDto;
import uz.uzum.springapptemplate.mapper.SpringAppTemplateMapper;
import uz.uzum.springapptemplate.model.SpringAppTemplateEntity;
import uz.uzum.springapptemplate.repository.SpringAppTemplateRepository;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class SpringAppTemplateService {

    private final SpringAppTemplateRepository repository;
    private final SpringAppTemplateMapper mapper;

    @Transactional(readOnly = true)
    public List<SpringAppTemplateDto> findAll() {
        log.debug("Получение всех записей");
        List<SpringAppTemplateEntity> entities = repository.findAll();
        return mapper.toDtoList(entities);
    }

    @Transactional(readOnly = true)
    public SpringAppTemplateDto findById(UUID id) {
        log.debug("Получение записи по id: {}", id);
        return repository
                .findById(id)
                .map(mapper::toDto)
                .orElseThrow(() -> new EntityNotFoundException("Запись с id " + id + " не найдена"));
    }

    public SpringAppTemplateDto create(SpringAppTemplateDto dto) {
        log.debug("Создание новой записи: {}", dto);
        SpringAppTemplateEntity entity = mapper.toEntity(dto);
        entity = repository.save(entity);
        return mapper.toDto(entity);
    }

    public SpringAppTemplateDto update(UUID id, SpringAppTemplateDto dto) {
        log.debug("Обновление записи с id {}: {}", id, dto);
        SpringAppTemplateEntity entity = repository
                .findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Запись с id " + id + " не найдена"));

        mapper.updateEntityFromDto(dto, entity);
        entity = repository.save(entity);
        return mapper.toDto(entity);
    }

    public void delete(UUID id) {
        log.debug("Удаление записи с id: {}", id);
        if (!repository.existsById(id)) {
            throw new EntityNotFoundException("Запись с id " + id + " не найдена");
        }
        repository.deleteById(id);
    }

    @Transactional(readOnly = true)
    public List<SpringAppTemplateDto> findAllActive() {
        log.debug("Получение всех активных записей");
        List<SpringAppTemplateEntity> entities = repository.findByIsActiveTrueOrderByPriorityDesc();
        return mapper.toDtoList(entities);
    }

    @Transactional(readOnly = true)
    public List<SpringAppTemplateDto> findByNameContaining(String name) {
        log.debug("Поиск записей по имени, содержащему: {}", name);
        List<SpringAppTemplateEntity> entities = repository.findByNameContainingIgnoreCase(name);
        return mapper.toDtoList(entities);
    }

    @Transactional(readOnly = true)
    public List<SpringAppTemplateDto> findWithPriorityGreaterThan(Integer priority) {
        log.debug("Поиск записей с приоритетом выше: {}", priority);
        List<SpringAppTemplateEntity> entities = repository.findEntitiesWithPriorityGreaterThan(priority);
        return mapper.toDtoList(entities);
    }
}
