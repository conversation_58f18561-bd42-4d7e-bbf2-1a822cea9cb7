package uz.uzum.springapptemplate.metrics;

import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@PropertySource("classpath:META-INF/build-info.properties")
@Component
public class BuildMetrics extends AbstractMetrics {

    private static final String BUILD_METRICS_NAME = "build_info_server";
    private static final String BUILD_TIME_KEY = "build.time";
    private static final String BUILD_VERSION_KEY = "build.version";

    public BuildMetrics(MeterRegistry registry, Environment env) {
        Gauge.builder(BUILD_METRICS_NAME, () -> 0)
                .tag("buildTime", getValue(env, BUILD_TIME_KEY))
                .tag("version", getValue(env, BUILD_VERSION_KEY))
                .description("Information about current application build")
                .register(registry);
    }
}
