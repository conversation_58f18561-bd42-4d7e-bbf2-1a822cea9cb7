package uz.uzum.springapptemplate.metrics;

import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@PropertySource("classpath:git.properties")
@Component
public class GitMetrics extends AbstractMetrics {

    private static final String GIT_METRICS_NAME = "git_commit_server";
    private static final String COMMIT_ID_KEY = "git.commit.id";
    private static final String BRANCH_KEY = "git.branch";
    private static final String COMMIT_MSG_KEY = "git.commit.message.full";
    private static final String COMMIT_TIME_KEY = "git.commit.time";
    private static final String AUTHOR_NAME = "git.commit.user.name";
    private static final String AUTHOR_EMAIL = "git.commit.user.email";

    public GitMetrics(MeterRegistry registry, Environment env) {
        Gauge.builder(GIT_METRICS_NAME, () -> 0)
                .tag("id", getValue(env, COMMIT_ID_KEY))
                .tag("branch", getValue(env, BRANCH_KEY))
                .tag("message", getValue(env, COMMIT_MSG_KEY))
                .tag("time", getValue(env, COMMIT_TIME_KEY))
                .tag("author", getAuthor(env))
                .description("Information about git commit that the build is based on")
                .register(registry);
    }

    private static String getAuthor(Environment env) {
        return getValue(env, AUTHOR_NAME) + " <" + getValue(env, AUTHOR_EMAIL) + ">";
    }
}
