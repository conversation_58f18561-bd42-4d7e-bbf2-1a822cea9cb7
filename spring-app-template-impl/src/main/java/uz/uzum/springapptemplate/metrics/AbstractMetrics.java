package uz.uzum.springapptemplate.metrics;

import static lombok.AccessLevel.PACKAGE;

import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;

@Slf4j
@NoArgsConstructor(access = PACKAGE)
public abstract class AbstractMetrics {

    private static final String NOT_AVAILABLE = "N/A";

    protected static String getValue(Environment env, String key) {
        if (env.getProperty(key) != null) {
            return env.getProperty(key);
        }
        log.warn("Key '{}' is not available in corresponding properties file.", key);
        return NOT_AVAILABLE;
    }
}
