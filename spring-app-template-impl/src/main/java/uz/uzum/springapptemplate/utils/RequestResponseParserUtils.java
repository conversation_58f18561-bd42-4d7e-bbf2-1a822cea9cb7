package uz.uzum.springapptemplate.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectReader;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import org.springframework.http.MediaType;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;

public final class RequestResponseParserUtils {

    private RequestResponseParserUtils() {}

    private static final String PARAMETERS_KEY = "parameters";

    public static ObjectNode getRequestText(ContentCachingRequestWrapper request) throws IOException {
        ObjectMapper mapper = new ObjectMapper();
        ObjectNode node = mapper.createObjectNode();

        node.put("method", request.getMethod());
        node.put("url", request.getRequestURI());
        if (request.getContentAsByteArray().length > 0) {
            ObjectReader reader = mapper.reader();
            node.set("body", reader.readTree(new ByteArrayInputStream(request.getContentAsByteArray())));
        }

        if (request.getParameterMap().size() > 0) {
            ObjectNode parameters = mapper.createObjectNode();
            request.getParameterMap().forEach((key, value) -> parameters.put(key, getParameter(value)));
            node.set(PARAMETERS_KEY, parameters);
        }

        return node;
    }

    public static ObjectNode getResponseText(ContentCachingResponseWrapper response) throws IOException {
        ObjectMapper mapper = new ObjectMapper();
        ObjectNode node = mapper.createObjectNode();
        XmlMapper xmlMapper = new XmlMapper();
        node.put("status", response.getStatus());
        if (response.getContentAsByteArray().length > 0) {
            ObjectReader reader = mapper.reader();
            if (response.getContentType().equals(MediaType.APPLICATION_XML_VALUE)) {
                node.set("body", xmlMapper.readTree(new ByteArrayInputStream(response.getContentAsByteArray())));
            } else {
                node.set("body", reader.readTree(new ByteArrayInputStream(response.getContentAsByteArray())));
            }
        }
        return node;
    }

    public static String getParameter(String[] value) {
        StringBuilder stringBuilder = new StringBuilder();
        for (String str : value) {
            stringBuilder.append(str);
        }
        return stringBuilder.toString();
    }
}
