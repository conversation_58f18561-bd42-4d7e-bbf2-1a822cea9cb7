package uz.uzum.springapptemplate.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.UUID;

public record SpringAppTemplateDto(
        UUID id,
        @NotBlank(message = "Имя не может быть пустым")
                @Size(max = 255, message = "Имя не может быть длиннее 255 символов")
                String name,
        @Size(max = 1000, message = "Описание не может быть длиннее 1000 символов") String description,
        @NotNull(message = "Поле isActive не может быть пустым") Boolean isActive,
        Integer priority) {}
