package uz.uzum.springapptemplate.repository;

import java.util.List;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import uz.uzum.springapptemplate.model.SpringAppTemplateEntity;

@Repository
public interface SpringAppTemplateRepository extends JpaRepository<SpringAppTemplateEntity, UUID> {

    List<SpringAppTemplateEntity> findByIsActiveTrueOrderByPriorityDesc();

    List<SpringAppTemplateEntity> findByNameContainingIgnoreCase(String name);

    @Query("SELECT e FROM SpringAppTemplateEntity e WHERE e.priority > :priority ORDER BY e.priority")
    List<SpringAppTemplateEntity> findEntitiesWithPriorityGreaterThan(@Param("priority") Integer priority);
}
