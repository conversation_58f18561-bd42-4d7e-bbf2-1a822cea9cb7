package uz.uzum.springapptemplate.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import uz.uzum.springapptemplate.dto.SpringAppTemplateDto;
import uz.uzum.springapptemplate.service.SpringAppTemplateService;

@RestController
@RequestMapping("/api/v1/templates")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Spring App Template Controller", description = "API для работы с сущностями приложения")
public class SpringAppTemplateController {

    private final SpringAppTemplateService service;

    @GetMapping
    @Operation(summary = "Получить все записи", description = "Возвращает список всех существующих записей")
    public ResponseEntity<List<SpringAppTemplateDto>> getAll() {
        log.info("Запрос на получение всех записей");
        return ResponseEntity.ok(service.findAll());
    }

    @GetMapping("/{id}")
    @Operation(summary = "Получить запись по ID", description = "Возвращает запись по указанному ID")
    public ResponseEntity<SpringAppTemplateDto> getById(@Parameter(description = "ID записи") @PathVariable UUID id) {
        log.info("Запрос на получение записи с id: {}", id);
        return ResponseEntity.ok(service.findById(id));
    }

    @PostMapping
    @Operation(summary = "Создать новую запись", description = "Создает новую запись на основе переданных данных")
    public ResponseEntity<SpringAppTemplateDto> create(
            @Parameter(description = "Данные для создания записи") @Valid @RequestBody SpringAppTemplateDto dto) {
        log.info("Запрос на создание новой записи: {}", dto);
        return new ResponseEntity<>(service.create(dto), HttpStatus.CREATED);
    }

    @PutMapping("/{id}")
    @Operation(summary = "Обновить запись", description = "Обновляет существующую запись по указанному ID")
    public ResponseEntity<SpringAppTemplateDto> update(
            @Parameter(description = "ID записи для обновления") @PathVariable UUID id,
            @Parameter(description = "Новые данные") @Valid @RequestBody SpringAppTemplateDto dto) {
        log.info("Запрос на обновление записи с id {}: {}", id, dto);
        return ResponseEntity.ok(service.update(id, dto));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Удалить запись", description = "Удаляет запись по указанному ID")
    public ResponseEntity<Void> delete(@Parameter(description = "ID записи для удаления") @PathVariable UUID id) {
        log.info("Запрос на удаление записи с id: {}", id);
        service.delete(id);
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/active")
    @Operation(
            summary = "Получить активные записи",
            description = "Возвращает список всех активных записей, отсортированных по приоритету")
    public ResponseEntity<List<SpringAppTemplateDto>> getAllActive() {
        log.info("Запрос на получение всех активных записей");
        return ResponseEntity.ok(service.findAllActive());
    }

    @GetMapping("/search")
    @Operation(
            summary = "Поиск по имени",
            description = "Возвращает записи, содержащие указанную строку в имени (без учета регистра)")
    public ResponseEntity<List<SpringAppTemplateDto>> searchByName(
            @Parameter(description = "Строка для поиска в имени") @RequestParam String name) {
        log.info("Запрос на поиск записей по имени: {}", name);
        return ResponseEntity.ok(service.findByNameContaining(name));
    }

    @GetMapping("/priority/{priority}")
    @Operation(summary = "Поиск по приоритету", description = "Возвращает записи с приоритетом выше указанного")
    public ResponseEntity<List<SpringAppTemplateDto>> getByPriorityGreaterThan(
            @Parameter(description = "Минимальный приоритет") @PathVariable Integer priority) {
        log.info("Запрос на поиск записей с приоритетом выше: {}", priority);
        return ResponseEntity.ok(service.findWithPriorityGreaterThan(priority));
    }
}
