pipeline:
  build:
    - graddle
  docker:
    - build-image
  test:
    - graddle-spotless
    - graddle-test
  swagger:
    - build-swagger
    - validate-swagger
    - trigger-swagger
  deploy:
    - deploy-preprod

variables:
  DB_URL: ****************************************
  POSTGRES_DB: postgres
  POSTGRES_USER: postgres
  POSTGRES_PASSWORD: postgres

services:
  - postgres

jobs:
  graddle:
    image: 8.6.0-jdk21-alpine
    artifacts:
      - spring-app-template-impl/build/libs/*.jar
      - spring-app-template-impl/build/classes/java/main/
      - spring-app-template-impl/build/resources/main/

  graddle-spotless:
    image: 8.6.0-jdk21-alpine
    script:
      - gradle spotlessCheck

  graddle-test:
    image: 8.6.0-jdk21-alpine
    script:
      - gradle test it -Dspring.profiles.active=it
    artifacts:
      - spring-app-template-impl/build/reports/tests/

  build-swagger:
    image: 8.6.0-jdk21-alpine
    type: graddle
    cmd: gradle
    options: clean generateOpenApiDocs
    settings: -Dspring.profiles.active=build -Dserver.port=8020
    swagger_file: spring-app-template-impl/build/swagger/swagger.yaml

  deploy-preprod:
    service: spring-app-template
