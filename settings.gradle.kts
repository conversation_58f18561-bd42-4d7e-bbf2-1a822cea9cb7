@file:Suppress("UnstableApiUsage")

pluginManagement {
    includeBuild("plugins")
    repositories {
        mavenLocal()
        gradlePluginPortal()
        mavenCentral()
        maven {
            url = uri("https://git.uzum.io/api/v4/groups/317/-/packages/maven")
            credentials(HttpHeaderCredentials::class) {
                val privateToken = providers.gradleProperty("PRIVATE_TOKEN").orNull
                name = if (privateToken != null) "PRIVATE-TOKEN" else "Authorization"
                value = privateToken ?: ("Bearer " + (System.getenv("READ_API_GROUP_ACCESS_TOKEN") ?: ""))
            }
            authentication {
                create<HttpHeaderAuthentication>("header")
            }
        }
    }
}

dependencyResolutionManagement {

    repositories {
        mavenCentral()
        mavenLocal()
        maven {
            url = uri("https://git.uzum.io/api/v4/groups/317/-/packages/maven")
            credentials(HttpHeaderCredentials::class) {
                val privateToken = providers.gradleProperty("PRIVATE_TOKEN").orNull
                name = if (privateToken != null) "PRIVATE-TOKEN" else "Authorization"
                value = privateToken ?: ("Bearer " + (System.getenv("READ_API_GROUP_ACCESS_TOKEN") ?: ""))
            }
            authentication {
                create<HttpHeaderAuthentication>("header")
            }
        }
    }

    versionCatalogs {
        create("libs") {
            from(files("gradle/catalogs/libs.versions.toml"))
        }
    }
}

buildCache {
    local {
        isEnabled = true
        directory = File(rootProject.projectDir, ".gradle/build-cache")
    }
}

rootProject.name = "spring-app-template"
include("spring-app-template-api")
include("spring-app-template-impl")
