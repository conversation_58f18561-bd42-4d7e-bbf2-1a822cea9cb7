import java.io.ByteArrayOutputStream
import java.io.File
import java.nio.file.Files
import java.nio.file.StandardCopyOption

tasks.register("generateService") {
    group = "template"
    description = "Generates a new service based on template without using bash scripts or Docker"

    doLast {
        val projectName = project.findProperty("projectName") as? String
            ?: throw GradleException("Project name is not specified. Use: ./gradlew generateService -PprojectName=my-service")

        val packagePrefix = project.findProperty("packagePrefix") as? String ?: "uz.uzum"

        val formattedServiceName = projectName.toLowerCase().replace("\\s+".toRegex(), "")

        if (!formattedServiceName.matches(Regex("^[a-z][-a-z0-9]*$"))) {
            throw GradleException("Invalid project name. Use only lowercase letters, numbers, and hyphens. Name must start with a letter.")
        }

        val gitUserName = getGitConfig("user.name") ?: "Template Generator"
        val gitUserEmail = getGitConfig("user.email") ?: "<EMAIL>"
        val gitConfig = "$gitUserName:$gitUserEmail"

        val templateDir = project.projectDir

        val targetDir = templateDir.parentFile.resolve(formattedServiceName)

        if (targetDir.exists()) {
            logger.warn("Target directory already exists: $targetDir")
            val deleteConfirmation = project.findProperty("forceDelete") as? String ?: "false"
            if (deleteConfirmation.toBoolean()) {
                logger.lifecycle("Deleting existing directory: $targetDir")
                targetDir.deleteRecursively()
            } else {
                throw GradleException("Target directory already exists: $targetDir. Use -PforceDelete=true to overwrite.")
            }
        }

        logger.lifecycle("Starting service generation...")
        logger.lifecycle("• Project name: $formattedServiceName")
        logger.lifecycle("• Package prefix: $packagePrefix")
        logger.lifecycle("• Git config: $gitConfig")
        logger.lifecycle("• Template directory: $templateDir")
        logger.lifecycle("• Target directory: $targetDir")
        logger.lifecycle("")

        try {
            copyTemplateWithExclusions(templateDir, targetDir)

            val projectNameNoHyphens = formattedServiceName.replace("-", "")
            val projectNamePascal = toPascalCase(formattedServiceName)
            val projectNameTitleCase = toTitleCase(formattedServiceName)

            createGitIgnore(targetDir)

            replaceInFiles(
                targetDir = targetDir,
                oldName = "spring-app-template",
                newName = formattedServiceName,
                oldNameNoHyphens = "springapptemplate",
                newNameNoHyphens = projectNameNoHyphens,
                oldNamePascal = "SpringAppTemplate",
                newNamePascal = projectNamePascal,
                oldNameTitleCase = "Spring App Template",
                newNameTitleCase = projectNameTitleCase,
                oldPackage = "uz.uzum.springapptemplate",
                newPackage = "$packagePrefix.$projectNameNoHyphens"
            )

            processRootBuildGradle(targetDir)

            renameModuleDirectories(
                targetDir = targetDir,
                oldName = "spring-app-template",
                newName = formattedServiceName
            )

            renamePackageDirectories(
                targetDir = targetDir,
                oldPackagePath = "uz/uzum/springapptemplate",
                newPackagePath = packagePrefix.replace('.', '/') + "/$projectNameNoHyphens"
            )

            updateSettingsGradle(
                targetDir = targetDir,
                oldName = "spring-app-template",
                newName = formattedServiceName
            )

            updateGitlabCi(
                targetDir = targetDir,
                oldName = "spring-app-template",
                newName = formattedServiceName
            )

            updateGrafanaDashboard(
                targetDir = targetDir,
                oldNameTitleCase = "Spring App Template",
                newNameTitleCase = projectNameTitleCase
            )

            updateDockerCompose(
                targetDir = targetDir,
                oldName = "spring-app-template",
                newName = formattedServiceName
            )

            updatePrometheusConfig(
                targetDir = targetDir,
                oldNameTitleCase = "Spring App Template",
                newNameTitleCase = projectNameTitleCase
            )

            createReadme(
                targetDir = targetDir,
                serviceName = formattedServiceName,
                serviceNameTitleCase = projectNameTitleCase,
                packageName = "$packagePrefix.$projectNameNoHyphens"
            )

            copyDependencies(templateDir, targetDir)

            initGitRepo(
                targetDir = targetDir,
                gitUserName = gitUserName,
                gitUserEmail = gitUserEmail
            )

            logger.lifecycle("Service successfully generated at: $targetDir")
            logger.lifecycle("")
            logger.lifecycle("Next steps:")
            logger.lifecycle("  1. cd $formattedServiceName")
            logger.lifecycle("  2. ./gradlew build")
            logger.lifecycle("  3. ./gradlew bootRun")

        } catch (e: Exception) {
            logger.error("Error generating service: ${e.message}")
            if (targetDir.exists()) {
                logger.warn("Cleaning up target directory due to error...")
                targetDir.deleteRecursively()
            }
            throw GradleException("Service generation failed", e)
        }
    }
}

fun getGitConfig(key: String): String? {
    val output = ByteArrayOutputStream()
    val result = project.exec {
        commandLine("git", "config", "--get", key)
        standardOutput = output
        isIgnoreExitValue = true
    }
    return if (result.exitValue == 0) output.toString().trim() else null
}

fun copyTemplateWithExclusions(srcDir: File, dstDir: File) {
    logger.lifecycle("Copying template from $srcDir to $dstDir with exclusions...")

    val srcPath = srcDir.toPath()
    val dstPath = dstDir.toPath()

    Files.createDirectories(dstPath)

    val excludeDirNames = setOf(
        "build",
        "bin",
        ".git",
        ".gradle",
        ".idea",
        "out",
        "target",
        ".kotlin"
    )

    val excludeRootDirs = setOf(
        "generator",
        dstDir.name
    )

    val excludeFiles = setOf(
        "README.md",
        ".DS_Store",
        "Thumbs.db",
        "generate-service.gradle.kts"
    )

    Files.walk(srcPath)
        .filter { path ->
            val relativePath = srcPath.relativize(path).toString()
            val pathParts = relativePath.split(File.separator, "/", "\\")

            val containsExcludedDir = pathParts.any { part ->
                excludeDirNames.contains(part)
            }

            val startsWithExcludedRootDir = excludeRootDirs.any { exclude ->
                relativePath == exclude ||
                        relativePath.startsWith("$exclude/") ||
                        relativePath.startsWith("$exclude\\")
            }

            val isExcludedFile = excludeFiles.contains(path.fileName.toString())

            !containsExcludedDir && !startsWithExcludedRootDir && !isExcludedFile
        }
        .forEach { path ->
            val relativePath = srcPath.relativize(path)
            val targetPath = dstPath.resolve(relativePath)

            if (Files.isDirectory(path)) {
                Files.createDirectories(targetPath)
                logger.debug("Created directory: {}", targetPath)
            } else {
                Files.createDirectories(targetPath.parent)

                Files.copy(path, targetPath, StandardCopyOption.REPLACE_EXISTING)
                logger.debug("Copied file: {}", targetPath)

                if (path.fileName.toString() == "gradlew") {
                    targetPath.toFile().setExecutable(true)
                    logger.debug("Set executable permission for: {}", targetPath)
                }
            }
        }

    logger.lifecycle("Template copied successfully")
}

fun copyDependencies(srcDir: File, dstDir: File) {
    logger.lifecycle("Copying dependencies and plugins...")

    val libsVersionsFile = srcDir.resolve("gradle/libs.versions.toml")
    if (libsVersionsFile.exists()) {
        val targetLibsDir = dstDir.resolve("gradle")
        targetLibsDir.mkdirs()
        libsVersionsFile.copyTo(targetLibsDir.resolve("libs.versions.toml"), overwrite = true)
    }

    val buildSrcDir = srcDir.resolve("buildSrc")
    if (buildSrcDir.exists() && buildSrcDir.isDirectory) {
        val targetBuildSrcDir = dstDir.resolve("buildSrc")
        buildSrcDir.copyRecursively(targetBuildSrcDir, overwrite = true)
    }

    logger.lifecycle("Dependencies and plugins copied successfully")
}

fun toPascalCase(input: String): String {
    return input.split("-", ".")
        .joinToString("") { it.capitalize() }
}

fun toTitleCase(input: String): String {
    return input.split("-", ".")
        .joinToString(" ") { it.capitalize() }
}

fun String.capitalize(): String {
    return if (isEmpty()) this else this[0].uppercaseChar() + substring(1)
}

fun replaceInFiles(
    targetDir: File,
    oldName: String,
    newName: String,
    oldNameNoHyphens: String,
    newNameNoHyphens: String,
    oldNamePascal: String,
    newNamePascal: String,
    oldNameTitleCase: String,
    newNameTitleCase: String,
    oldPackage: String,
    newPackage: String
) {
    logger.lifecycle("Replacing text in files...")

    val fileExtensions = setOf(
        "java", "kt", "kts", "xml", "yml", "yaml", "properties",
        "md", "html", "js", "css", "txt", "json"
    )

    val oldNameCamel = oldNameNoHyphens.toCamelCase()
    val newNameCamel = newNameNoHyphens.toCamelCase()

    val additionalPatterns = mapOf(
        "SpringAppTemplateTest" to "${newNamePascal}Test",
        "SpringAppTemplateIT" to "${newNamePascal}IT",
        "TestSpringAppTemplate" to "Test${newNamePascal}",
        "IntegrationTest" to "IntegrationTest",

        "${oldNamePascal}Dto" to "${newNamePascal}Dto",
        "${oldNamePascal}Request" to "${newNamePascal}Request",
        "${oldNamePascal}Response" to "${newNamePascal}Response",
        "${oldNamePascal}Controller" to "${newNamePascal}Controller",
        "${oldNamePascal}Api" to "${newNamePascal}Api",
        "${oldNamePascal}Client" to "${newNamePascal}Client",
        "${oldNamePascal}Service" to "${newNamePascal}Service",
        "${oldNamePascal}Repository" to "${newNamePascal}Repository",
        "${oldNamePascal}Entity" to "${newNamePascal}Entity",
        "${oldNamePascal}Mapper" to "${newNamePascal}Mapper",
        "${oldNamePascal}Exception" to "${newNamePascal}Exception",
        "${oldNamePascal}Config" to "${newNamePascal}Config",

        oldNameCamel to newNameCamel,
        "${oldNameNoHyphens}Dto" to "${newNameNoHyphens}Dto",

        "/${oldName}" to "/${newName}",
        "/${oldNameNoHyphens}" to "/${newNameNoHyphens}",
        "/api/${oldName}" to "/api/${newName}",
        "/api/${oldNameNoHyphens}" to "/api/${newNameNoHyphens}",
        "/v1/${oldName}" to "/v1/${newName}",
        "/v1/${oldNameNoHyphens}" to "/v1/${newNameNoHyphens}",

        "${oldNameNoHyphens}Variable" to "${newNameNoHyphens}Variable",
        "${oldName.toUpperCase().replace('-', '_')}" to "${newName.toUpperCase().replace('-', '_')}"
    )

    targetDir.walkTopDown()
        .filter { file ->
            file.isFile &&
                    fileExtensions.any { ext -> file.name.endsWith(".$ext") }
        }
        .forEach { file ->
            logger.debug("Processing file: {}", file.relativeTo(targetDir))

            var content = file.readText()

            content = content
                .replace(oldName, newName)
                .replace(oldNameNoHyphens, newNameNoHyphens)
                .replace(oldNamePascal, newNamePascal)
                .replace(oldNameTitleCase, newNameTitleCase)
                .replace(oldPackage, newPackage)
                .replace(oldNameCamel, newNameCamel)

            additionalPatterns.forEach { (oldPattern, newPattern) ->
                content = content.replace(oldPattern, newPattern)
            }

            file.writeText(content)
        }

    val filePatternsToRename = listOf(
        oldNamePascal to newNamePascal,
        "${oldNamePascal}Test" to "${newNamePascal}Test",
        "${oldNamePascal}IT" to "${newNamePascal}IT",
        "Test${oldNamePascal}" to "Test${newNamePascal}",
        "${oldNamePascal}Dto" to "${newNamePascal}Dto",
        "${oldNamePascal}Request" to "${newNamePascal}Request",
        "${oldNamePascal}Response" to "${newNamePascal}Response",
        "${oldNamePascal}Controller" to "${newNamePascal}Controller",
        "${oldNamePascal}Api" to "${newNamePascal}Api",
        "${oldNamePascal}Client" to "${newNamePascal}Client",
        "${oldNamePascal}Service" to "${newNamePascal}Service",
        "${oldNamePascal}Repository" to "${newNamePascal}Repository",
        "${oldNamePascal}Entity" to "${newNamePascal}Entity",
        "${oldNamePascal}Mapper" to "${newNamePascal}Mapper",
        "${oldNamePascal}Exception" to "${newNamePascal}Exception",
        "${oldNamePascal}Config" to "${newNamePascal}Config"
    )

    targetDir.walkTopDown()
        .filter { file ->
            file.isFile &&
                    (file.name.endsWith(".java") || file.name.endsWith(".kt")) &&
                    filePatternsToRename.any { (pattern, _) -> file.name.contains(pattern) }
        }
        .forEach { file ->
            var newFileName = file.name
            filePatternsToRename.forEach { (oldPattern, newPattern) ->
                if (file.name.contains(oldPattern)) {
                    newFileName = newFileName.replace(oldPattern, newPattern)
                }
            }

            if (newFileName != file.name) {
                val newFile = File(file.parentFile, newFileName)
                logger.lifecycle("Renaming file: ${file.name} to ${newFile.name}")
                file.renameTo(newFile)
            }
        }

    logger.lifecycle("Text replacement completed")
}

fun String.toCamelCase(): String {
    return if (isEmpty()) this
    else this[0].lowercaseChar() + substring(1)
}

fun renameModuleDirectories(targetDir: File, oldName: String, newName: String) {
    logger.lifecycle("Renaming module directories...")

    val apiDir = targetDir.resolve("$oldName-api")
    val implDir = targetDir.resolve("$oldName-impl")

    if (apiDir.exists()) {
        val newApiDir = targetDir.resolve("$newName-api")
        logger.lifecycle("Renaming ${apiDir.name} to ${newApiDir.name}")
        apiDir.renameTo(newApiDir)
    } else {
        logger.warn("API module directory not found: $apiDir")
    }

    if (implDir.exists()) {
        val newImplDir = targetDir.resolve("$newName-impl")
        logger.lifecycle("Renaming ${implDir.name} to ${newImplDir.name}")
        implDir.renameTo(newImplDir)
    } else {
        logger.warn("IMPL module directory not found: $implDir")
    }

    logger.lifecycle("Module directories renamed successfully")
}

fun renamePackageDirectories(targetDir: File, oldPackagePath: String, newPackagePath: String) {
    logger.lifecycle("Renaming package directories from $oldPackagePath to $newPackagePath...")

    val packageDirs = mutableListOf<File>()

    targetDir.walkTopDown()
        .filter { file ->
            file.isDirectory &&
                    file.path.contains("src/main/java") &&
                    file.path.endsWith(oldPackagePath.replace('/', File.separatorChar))
        }
        .forEach { packageDirs.add(it) }

    targetDir.walkTopDown()
        .filter { file ->
            file.isDirectory &&
                    file.path.contains("src/test/java") &&
                    file.path.endsWith(oldPackagePath.replace('/', File.separatorChar))
        }
        .forEach { packageDirs.add(it) }

    packageDirs.forEach { oldDir ->
        val relativePath = oldDir.relativeTo(targetDir).path
        val newRelativePath = relativePath.replace(
            oldPackagePath.replace('/', File.separatorChar),
            newPackagePath.replace('/', File.separatorChar)
        )
        val newDir = File(targetDir, newRelativePath)

        logger.lifecycle("Creating new package directory: $newRelativePath")
        newDir.mkdirs()

        oldDir.walkTopDown()
            .filter { it.isFile }
            .forEach { file ->
                val relativeFilePath = file.relativeTo(oldDir).path
                val newFile = File(newDir, relativeFilePath)
                newFile.parentFile.mkdirs()
                file.copyTo(newFile, overwrite = true)
            }

        if (oldDir.exists()) {
            logger.lifecycle("Deleting old package directory: ${oldDir.relativeTo(targetDir)}")
            oldDir.deleteRecursively()
        }
    }

    logger.lifecycle("Package directories renamed successfully")
}

fun updateSettingsGradle(targetDir: File, oldName: String, newName: String) {
    val settingsFile = targetDir.resolve("settings.gradle.kts")
    if (settingsFile.exists()) {
        logger.lifecycle("Updating settings.gradle.kts...")

        var content = settingsFile.readText()
        content = content
            .replace("rootProject.name = \"$oldName\"", "rootProject.name = \"$newName\"")
            .replace("include(\"$oldName-api\")", "include(\"$newName-api\")")
            .replace("include(\"$oldName-impl\")", "include(\"$newName-impl\")")

        settingsFile.writeText(content)
        logger.lifecycle("settings.gradle.kts updated successfully")
    } else {
        logger.warn("settings.gradle.kts not found")
    }
}

fun updateGitlabCi(targetDir: File, oldName: String, newName: String) {
    val gitlabCiFile = targetDir.resolve(".gitlab-ci.yml")
    if (gitlabCiFile.exists()) {
        logger.lifecycle("Updating .gitlab-ci.yml...")

        var content = gitlabCiFile.readText()
        content = content.replace(oldName, newName)

        gitlabCiFile.writeText(content)
        logger.lifecycle(".gitlab-ci.yml updated successfully")
    } else {
        logger.warn(".gitlab-ci.yml not found")
    }
}

fun updateGrafanaDashboard(targetDir: File, oldNameTitleCase: String, newNameTitleCase: String) {
    logger.lifecycle("Updating Grafana dashboard title...")

    val dashboardFile = targetDir.resolve("config/grafana/dashboards/dashboard.json")
    if (dashboardFile.exists()) {
        var content = dashboardFile.readText()

        val oldTitleWithPrefix = "\"title\": \"$oldNameTitleCase - Java Micrometer\""
        val newTitleWithPrefix = "\"title\": \"$newNameTitleCase - Java Micrometer\""

        val plainTitle = "\"title\": \"Java Micrometer\""

        content = if (content.contains(oldTitleWithPrefix)) {
            content.replace(oldTitleWithPrefix, newTitleWithPrefix)
        } else if (content.contains(plainTitle)) {
            content.replace(plainTitle, newTitleWithPrefix)
        } else {
            logger.warn("Could not find expected dashboard title pattern in the file")
            content
        }

        dashboardFile.writeText(content)
        logger.lifecycle("Grafana dashboard title updated successfully")
    } else {
        logger.warn("Grafana dashboard file not found: $dashboardFile")
    }
}

fun updateDockerCompose(targetDir: File, oldName: String, newName: String) {
    logger.lifecycle("Updating docker-compose.yaml...")

    val dockerComposeFile = targetDir.resolve("docker-compose.yaml")
    if (dockerComposeFile.exists()) {
        var content = dockerComposeFile.readText()

        content = content
            .replace("container_name: \"$oldName-prometheus\"", "container_name: \"$newName-prometheus\"")
            .replace("container_name: \"$oldName-grafana\"", "container_name: \"$newName-grafana\"")
            .replace("$oldName: null", "$newName: null")

        dockerComposeFile.writeText(content)
        logger.lifecycle("docker-compose.yaml updated successfully")
    } else {
        logger.warn("docker-compose.yaml not found")
    }
}

fun updatePrometheusConfig(targetDir: File, oldNameTitleCase: String, newNameTitleCase: String) {
    logger.lifecycle("Updating Prometheus configuration...")

    val prometheusConfigFile = targetDir.resolve("config/prometheus.yaml")
    if (prometheusConfigFile.exists()) {
        var content = prometheusConfigFile.readText()

        val oldJobName = "job_name: \"$oldNameTitleCase - Micrometer Server monitoring\""
        val newJobName = "job_name: \"$newNameTitleCase - Micrometer Server monitoring\""

        val plainJobName = "job_name: \"Micrometer Server monitoring\""

        content = if (content.contains(oldJobName)) {
            content.replace(oldJobName, newJobName)
        } else if (content.contains(plainJobName)) {
            content.replace(plainJobName, newJobName)
        } else {
            logger.warn("Could not find expected job_name pattern in the Prometheus config file")
            content
        }

        prometheusConfigFile.writeText(content)
        logger.lifecycle("Prometheus configuration updated successfully")
    } else {
        logger.warn("Prometheus configuration file not found: $prometheusConfigFile")
    }
}

fun createReadme(targetDir: File, serviceName: String, serviceNameTitleCase: String, packageName: String) {
    logger.lifecycle("Creating README.md...")

    val readmeContent = """
        # $serviceNameTitleCase

        Микросервис $serviceNameTitleCase, созданный на основе шаблона Spring Boot.

        ## Технологии

        - Java 21
        - Spring Boot 3.4.2
        - Spring Cloud 2024.0.0
        - Gradle 8.13
        - PostgreSQL
        - Testcontainers
        - Prometheus + Grafana для мониторинга

        ## Структура проекта

        - `$serviceName-api` - API модуль с DTO и интерфейсами
        - `$serviceName-impl` - Имплементация сервиса

        ## Запуск локально
        
        ### Запуск окружения
        
        Для локального запуска предварительно нужно запустить окружение через docker-compose.yml из корня проекта:
        
        ```bash
        docker-compose up -d
        ```
        (конкретно тут нам пригодится БД postgres)

        ### Запуск сервиса

        ```bash
        ./gradlew bootRun
        ```
        или через IDE.
        
        Приложение по умолчанию запускается на порту 8080, с профилем local, без необходимости указывать его явно.

        ### Организация тестов

        Для правильной организации тестов следуйте этим правилам:

        #### Юнит-тесты

        - Размещайте в папке `src/test/java/.../unit`
        - Помечайте класс аннотацией `@UnitTest`
        - Запускаются командой `./gradlew test`

        #### Интеграционные тесты

        - Размещайте в папке `src/test/java/.../integration`
        - Помечайте класс аннотацией `@IntegrationTest`
        - Запускаются командой `./gradlew it`

        Это разделение позволяет запускать быстрые юнит-тесты отдельно от более длительных интеграционных тестов.
        
        #### Для запуска всех тестов используйте: 
        
        ```bash
        ./gradlew check
        ```

        ## Мониторинг

        Для запуска мониторинга используйте:

        ```bash
        docker-compose up -d
        ```

        - Prometheus: http://localhost:9090
        - Grafana: http://localhost:3000 (admin/admin)

        ### Дашборд Grafana

        Дашборд "$serviceNameTitleCase - Java Micrometer" будет автоматически загружен в Grafana при запуске контейнеров. Чтобы просмотреть его:

        1. Откройте Grafana по адресу http://localhost:3000 и войдите с учетными данными admin/admin
        2. Нажмите на иконку дашбордов в левом меню
        3. Вы увидите дашборд "$serviceNameTitleCase - Java Micrometer" в списке доступных дашбордов

        Дашборд показывает основные метрики приложения: JVM, HTTP запросы, база данных и другие показатели.

        ## API документация и генерация кода

        Swagger UI доступен по адресу: http://localhost:8080/swagger-ui.html

        ### Генерация DTO и интерфейсов API

        В модуле API (`$serviceName-api`) интерфейсы и DTO классы генерируются автоматически на основе OpenAPI спецификации. Процесс генерации:

        1. Сначала генерируется OpenAPI спецификация из кода в модуле IMPL:
        ```bash
        ./gradlew generateOpenApiDocs
        ```

        2. Затем на основе этой спецификации генерируются интерфейсы и DTO в модуле API:
        ```bash
        ./gradlew openApiGenerate
        ```

        3. Для выполнения обоих шагов сразу можно использовать:
        ```bash
        ./gradlew clean generateOpenApiDocs openApiGenerate
        ```

        **Важно:** Не редактируйте сгенерированные файлы вручную, так как они будут перезаписаны при следующей генерации. Вместо этого изменяйте контроллеры и модели в модуле IMPL.

        ## Важные настройки

        ### Настройка доступа к репозиторию

        Иногда IntelliJ IDEA некорректно работает с системными проперти (PRIVATE_TOKEN в нашем случае).
        В таком случае необходимо настроить переменную окружения `READ_API_GROUP_ACCESS_TOKEN` с токеном доступа к GitLab API:

        1. Получите персональный токен в GitLab: Профиль -> Preferences -> Access Tokens
        2. Создайте токен с правами `read_api`
        3. Добавьте переменную окружения в вашу систему:

        ```bash
        # Для Linux/macOS (добавьте в ~/.bashrc или ~/.zshrc)
        export READ_API_GROUP_ACCESS_TOKEN="ваш_токен"

        # После добавления в файл, примените изменения:
        source ~/.zshrc  # или source ~/.bashrc

        # Для Windows (PowerShell)
        [Environment]::SetEnvironmentVariable("READ_API_GROUP_ACCESS_TOKEN", "ваш_токен", "User")
        # После этого нужно перезапустить PowerShell или терминал
        ```

        4. Если у вас возникают проблемы с кэшем Gradle, выполните:

        ```bash
        ./gradlew --stop
        ./gradlew build
        ```

        В IntelliJ IDEA может потребоваться перезапуск для применения новой переменной окружения.

        ### GitLab репозиторий

        После создания проекта в GitLab, необходимо обновить ID проекта в файле `$serviceName-api/build.gradle.kts`:

        ```kotlin
        repositories {
            mavenLocal()
            maven {
                name = "GitLab"
                // Заменить REPLACE_WITH_YOUR_GITLAB_PROJECT_ID на реальный ID проекта в GitLab
                url = uri("https://git.uzum.io/api/v4/projects/REPLACE_WITH_YOUR_GITLAB_PROJECT_ID/packages/maven")
                credentials(HttpHeaderCredentials::class) {
                    name = "Job-Token"
                    value = System.getenv("CI_JOB_TOKEN")
                }
                authentication {
                    create<HttpHeaderAuthentication>("header")
                }
            }
        }
        ```

        ## Основной пакет

        `$packageName`
    """.trimIndent()

    val readmeFile = targetDir.resolve("README.md")
    readmeFile.writeText(readmeContent)

    logger.lifecycle("README.md created successfully")
}

fun createGitIgnore(targetDir: File) {
    logger.lifecycle("Creating .gitignore file...")

    val gitIgnoreContent = """
        # Project and build files
        HELP.md
        .gradle/
        .kotlin/
        build/
        target/
        out/
        bin/

        # IDE - IntelliJ IDEA
        .idea/
        *.iws
        *.iml
        *.ipr

        # IDE - VS Code
        .vscode/

        # System files
        .DS_Store
        Thumbs.db

        # Logs
        *.log
        logs/

        # Temporary files
        *.tmp
        *.temp

        # OpenAPI Generator
        .openapi-generator/
        .openapi-generator-ignore

        # Keep Gradle wrapper
        !gradle/wrapper/gradle-wrapper.jar
        !gradle/wrapper/gradle-wrapper.properties
    """.trimIndent()

    val gitIgnoreFile = targetDir.resolve(".gitignore")
    gitIgnoreFile.writeText(gitIgnoreContent)

    logger.lifecycle(".gitignore file created successfully")
}

fun processRootBuildGradle(targetDir: File) {
    logger.lifecycle("Processing root build.gradle.kts...")

    val buildGradleFile = targetDir.resolve("build.gradle.kts")
    if (buildGradleFile.exists()) {
        var content = buildGradleFile.readText()

        val regex = Regex("\\s*apply\\(from\\s*=\\s*\"generate-service\\.gradle\\.kts\"\\)\\s*")
        content = regex.replace(content, "\n\n")

        buildGradleFile.writeText(content)
        logger.lifecycle("Root build.gradle.kts processed successfully")
    } else {
        logger.warn("Root build.gradle.kts not found")
    }
}

fun initGitRepo(targetDir: File, gitUserName: String, gitUserEmail: String) {
    logger.lifecycle("Initializing Git repository...")

    project.exec {
        workingDir = targetDir
        commandLine("git", "init")
    }

    project.exec {
        workingDir = targetDir
        commandLine("git", "branch", "-M", "main")
    }

    project.exec {
        workingDir = targetDir
        commandLine("git", "add", ".")
    }

    project.exec {
        workingDir = targetDir
        commandLine("git", "config", "user.name", gitUserName)
        isIgnoreExitValue = true
    }

    project.exec {
        workingDir = targetDir
        commandLine("git", "config", "user.email", gitUserEmail)
        isIgnoreExitValue = true
    }

    project.exec {
        workingDir = targetDir
        commandLine("git", "commit", "-m", "Initial commit from template")
        isIgnoreExitValue = true
    }

    logger.lifecycle("Git repository initialized successfully")
}
