# Spring Boot App Template

Шаблон для создания новых Spring Boot сервисов в Uzum.

## Создание нового сервиса

1. Клонировать репозиторий шаблона и перейти в его папку:
   ```bash
   <NAME_EMAIL>:bank/backend/spring-app-template.git
   cd spring-app-template
   ```

2. Запустить градл таску, указав название нового сервиса:
   ```bash
   ./gradlew generateService -PprojectName=your-service-name
   ```

3. Градл автоматически:
   - Создаст папку с новым проектом с указанным именем в родительской директории этого проекта
   - Настроит структуру пакетов и классов
   - Обновит все необходимые файлы конфигурации
   - Инициализирует Git репозиторий

## Дальнейшие инструкции

Подробная документация по использованию нового сервиса находится в README.md, который автоматически создается в папке с новым проектом.
