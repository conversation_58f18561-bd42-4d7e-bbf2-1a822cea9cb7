[versions]
# Spring
springBoot = "3.4.5"
springDependencyManagement = "1.1.7"

# OpenAPI
springDoc = "2.8.5"
swagger = "2.2.28"
openapi = "7.12.0"
springdocGradle = "1.9.0"

# Tools & Plugins
lombok = "8.13"
jib = "3.4.5"
gitProperties = "2.5.0"
mapstruct = "1.6.3"
hamcrest = "3.0"

# Uzum
uzumMigration = "1.0.0"
logformatStarter = "1.0.13-spring-boot-3"
uzumBankJavaFormat = "0.0.5"

[libraries]
# Spring
spring-boot-starter-web = { group = "org.springframework.boot", name = "spring-boot-starter-web" }
spring-boot-starter-data-jpa = { group = "org.springframework.boot", name = "spring-boot-starter-data-jpa" }
spring-boot-starter-validation = { group = "org.springframework.boot", name = "spring-boot-starter-validation" }
spring-boot-starter-actuator = { group = "org.springframework.boot", name = "spring-boot-starter-actuator" }
spring-boot-starter-test = { group = "org.springframework.boot", name = "spring-boot-starter-test" }
spring-boot-starter-webflux = { group = "org.springframework.boot", name = "spring-boot-starter-webflux" }
spring-cloud-starter-openfeign = { group = "org.springframework.cloud", name = "spring-cloud-starter-openfeign" }
spring-webmvc = { group = "org.springframework", name = "spring-webmvc" }

# Jakarta
jakarta-validation-api = { group = "jakarta.validation", name = "jakarta.validation-api" }
jakarta-annotation-api = { group = "jakarta.annotation", name = "jakarta.annotation-api" }
jakarta-servlet-api = { group = "jakarta.servlet", name = "jakarta.servlet-api" }

# OpenAPI
springdoc-openapi = { group = "org.springdoc", name = "springdoc-openapi-starter-webmvc-ui", version.ref = "springDoc" }
swagger-annotations = { group = "io.swagger.core.v3", name = "swagger-annotations-jakarta" , version.ref = "swagger" }

# Database
postgresql = { group = "org.postgresql", name = "postgresql" }
liquibase = { group = "org.liquibase", name = "liquibase-core" }

# Mapping
mapstruct = { group = "org.mapstruct", name = "mapstruct", version.ref = "mapstruct" }
mapstruct-processor = { group = "org.mapstruct", name = "mapstruct-processor", version.ref = "mapstruct" }

# Monitoring & Logging
micrometer-prometheus = { group = "io.micrometer", name = "micrometer-registry-prometheus" }
sentry-starter = { group = "io.sentry", name = "sentry-spring-boot-starter-jakarta" }
logformat-starter = { group = "uz.uzum", name = "log-formatter", version.ref = "logformatStarter" }

# Testing
testcontainers-junit = { group = "org.testcontainers", name = "junit-jupiter" }
testcontainers-postgresql = { group = "org.testcontainers", name = "postgresql" }
mockito = { group = "org.mockito", name = "mockito-junit-jupiter" }
hamcrest = { group = "org.hamcrest", name = "hamcrest", version.ref = "hamcrest" }

# WebJars
webjars-locator-core = { group = "org.webjars", name = "webjars-locator-core" }
webjars-swagger-ui = { group = "org.webjars", name = "swagger-ui" }

# Jackson
jackson-dataformat-xml = { group = "com.fasterxml.jackson.dataformat", name = "jackson-dataformat-xml" }
jackson-databind = { group = "com.fasterxml.jackson.core", name = "jackson-databind" }

# Plugins
[plugins]
spring-boot = { id = "org.springframework.boot", version.ref = "springBoot" }
spring-dependency-management = { id = "io.spring.dependency-management", version.ref = "springDependencyManagement" }
lombok = { id = "io.freefair.lombok", version.ref = "lombok" }
jib = { id = "com.google.cloud.tools.jib", version.ref = "jib" }
git-properties = { id = "com.gorylenko.gradle-git-properties", version.ref = "gitProperties" }
springdoc-openapi = { id = "org.springdoc.openapi-gradle-plugin", version.ref = "springdocGradle" }
openapi = { id = "org.openapi.generator", version.ref = "openapi" }
uzum-migration = { id = "uz.uzum.migration", version.ref = "uzumMigration" }
uzumbank-javaformat = { id = "uz.uzum.uzumbank-javaformat", version.ref = "uzumBankJavaFormat" }
