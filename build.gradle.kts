import io.freefair.gradle.plugins.lombok.LombokPlugin
import io.spring.gradle.dependencymanagement.DependencyManagementPlugin

plugins {
    alias(libs.plugins.spring.boot) apply false
    alias(libs.plugins.spring.dependency.management)
    alias(libs.plugins.lombok) apply false
    alias(libs.plugins.jib) apply false
    alias(libs.plugins.springdoc.openapi) apply false
    alias(libs.plugins.uzumbank.javaformat)
    alias(libs.plugins.uzum.migration)
    java
}

apply(from = "generate-service.gradle.kts")

allprojects {

    tasks.configureEach {
        outputs.cacheIf { true }
    }
}

subprojects {
    apply<JavaPlugin>()
    apply<LombokPlugin>()
    apply<DependencyManagementPlugin>()

    group = "uz.uzum"
    version = "0.0.1"

    dependencyManagement {
        imports {
            mavenBom(
                "org.springframework.boot:spring-boot-dependencies:${
                    providers.gradleProperty("springBootVersion").get()
                }",
            )
            mavenBom(
                "org.springframework.cloud:spring-cloud-dependencies:${
                    providers.gradleProperty("springCloudVersion").get()
                }",
            )
            mavenBom("org.testcontainers:testcontainers-bom:${providers.gradleProperty("testcontainersVersion").get()}")
            mavenBom("org.junit:junit-bom:${providers.gradleProperty("junitVersion").get()}")
            mavenBom("io.sentry:sentry-bom:${providers.gradleProperty("sentryVersion").get()}")
        }
    }

    tasks.jar {
        enabled = false
    }

    java {
        sourceCompatibility = JavaVersion.VERSION_21
        targetCompatibility = JavaVersion.VERSION_21
    }
}

tasks.named("clean") {
    dependsOn(gradle.includedBuild("plugins").task(":clean"))
}
