/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (7.12.0).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
package uz.uzum.springapptemplate.api;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Generated;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.UUID;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import uz.uzum.springapptemplate.model.SpringAppTemplateDto;

@Generated(value = "org.openapitools.codegen.languages.SpringCodegen", comments = "Generator version: 7.12.0")
@Validated
@Tag(name = "Spring App Template Controller", description = "API для работы с сущностями приложения")
public interface SpringAppTemplateControllerApi {

    /**
     * POST /api/v1/templates : Создать новую запись
     * Создает новую запись на основе переданных данных
     *
     * @param springAppTemplateDto (required)
     * @return OK (status code 200)
     */
    @Operation(
            operationId = "create",
            summary = "Создать новую запись",
            description = "Создает новую запись на основе переданных данных",
            tags = {"Spring App Template Controller"},
            responses = {
                @ApiResponse(
                        responseCode = "200",
                        description = "OK",
                        content = {
                            @Content(mediaType = "*/*", schema = @Schema(implementation = SpringAppTemplateDto.class))
                        })
            })
    @RequestMapping(
            method = RequestMethod.POST,
            value = "/api/v1/templates",
            produces = {"*/*"},
            consumes = {"application/json"})
    ResponseEntity<SpringAppTemplateDto> create(
            @Parameter(name = "SpringAppTemplateDto", description = "", required = true) @Valid @RequestBody
                    SpringAppTemplateDto springAppTemplateDto);

    /**
     * DELETE /api/v1/templates/{id} : Удалить запись
     * Удаляет запись по указанному ID
     *
     * @param id ID записи для удаления (required)
     * @return OK (status code 200)
     */
    @Operation(
            operationId = "delete",
            summary = "Удалить запись",
            description = "Удаляет запись по указанному ID",
            tags = {"Spring App Template Controller"},
            responses = {@ApiResponse(responseCode = "200", description = "OK")})
    @RequestMapping(method = RequestMethod.DELETE, value = "/api/v1/templates/{id}")
    ResponseEntity<Void> delete(
            @Parameter(name = "id", description = "ID записи для удаления", required = true, in = ParameterIn.PATH)
                    @PathVariable("id")
                    UUID id);

    /**
     * GET /api/v1/templates : Получить все записи
     * Возвращает список всех существующих записей
     *
     * @return OK (status code 200)
     */
    @Operation(
            operationId = "getAll",
            summary = "Получить все записи",
            description = "Возвращает список всех существующих записей",
            tags = {"Spring App Template Controller"},
            responses = {
                @ApiResponse(
                        responseCode = "200",
                        description = "OK",
                        content = {
                            @Content(
                                    mediaType = "*/*",
                                    array = @ArraySchema(schema = @Schema(implementation = SpringAppTemplateDto.class)))
                        })
            })
    @RequestMapping(
            method = RequestMethod.GET,
            value = "/api/v1/templates",
            produces = {"*/*"})
    ResponseEntity<List<SpringAppTemplateDto>> getAll();

    /**
     * GET /api/v1/templates/active : Получить активные записи
     * Возвращает список всех активных записей, отсортированных по приоритету
     *
     * @return OK (status code 200)
     */
    @Operation(
            operationId = "getAllActive",
            summary = "Получить активные записи",
            description = "Возвращает список всех активных записей, отсортированных по приоритету",
            tags = {"Spring App Template Controller"},
            responses = {
                @ApiResponse(
                        responseCode = "200",
                        description = "OK",
                        content = {
                            @Content(
                                    mediaType = "*/*",
                                    array = @ArraySchema(schema = @Schema(implementation = SpringAppTemplateDto.class)))
                        })
            })
    @RequestMapping(
            method = RequestMethod.GET,
            value = "/api/v1/templates/active",
            produces = {"*/*"})
    ResponseEntity<List<SpringAppTemplateDto>> getAllActive();

    /**
     * GET /api/v1/templates/{id} : Получить запись по ID
     * Возвращает запись по указанному ID
     *
     * @param id ID записи (required)
     * @return OK (status code 200)
     */
    @Operation(
            operationId = "getById",
            summary = "Получить запись по ID",
            description = "Возвращает запись по указанному ID",
            tags = {"Spring App Template Controller"},
            responses = {
                @ApiResponse(
                        responseCode = "200",
                        description = "OK",
                        content = {
                            @Content(mediaType = "*/*", schema = @Schema(implementation = SpringAppTemplateDto.class))
                        })
            })
    @RequestMapping(
            method = RequestMethod.GET,
            value = "/api/v1/templates/{id}",
            produces = {"*/*"})
    ResponseEntity<SpringAppTemplateDto> getById(
            @Parameter(name = "id", description = "ID записи", required = true, in = ParameterIn.PATH)
                    @PathVariable("id")
                    UUID id);

    /**
     * GET /api/v1/templates/priority/{priority} : Поиск по приоритету
     * Возвращает записи с приоритетом выше указанного
     *
     * @param priority Минимальный приоритет (required)
     * @return OK (status code 200)
     */
    @Operation(
            operationId = "getByPriorityGreaterThan",
            summary = "Поиск по приоритету",
            description = "Возвращает записи с приоритетом выше указанного",
            tags = {"Spring App Template Controller"},
            responses = {
                @ApiResponse(
                        responseCode = "200",
                        description = "OK",
                        content = {
                            @Content(
                                    mediaType = "*/*",
                                    array = @ArraySchema(schema = @Schema(implementation = SpringAppTemplateDto.class)))
                        })
            })
    @RequestMapping(
            method = RequestMethod.GET,
            value = "/api/v1/templates/priority/{priority}",
            produces = {"*/*"})
    ResponseEntity<List<SpringAppTemplateDto>> getByPriorityGreaterThan(
            @Parameter(name = "priority", description = "Минимальный приоритет", required = true, in = ParameterIn.PATH)
                    @PathVariable("priority")
                    Integer priority);

    /**
     * GET /api/v1/templates/search : Поиск по имени
     * Возвращает записи, содержащие указанную строку в имени (без учета регистра)
     *
     * @param name Строка для поиска в имени (required)
     * @return OK (status code 200)
     */
    @Operation(
            operationId = "searchByName",
            summary = "Поиск по имени",
            description = "Возвращает записи, содержащие указанную строку в имени (без учета регистра)",
            tags = {"Spring App Template Controller"},
            responses = {
                @ApiResponse(
                        responseCode = "200",
                        description = "OK",
                        content = {
                            @Content(
                                    mediaType = "*/*",
                                    array = @ArraySchema(schema = @Schema(implementation = SpringAppTemplateDto.class)))
                        })
            })
    @RequestMapping(
            method = RequestMethod.GET,
            value = "/api/v1/templates/search",
            produces = {"*/*"})
    ResponseEntity<List<SpringAppTemplateDto>> searchByName(
            @NotNull
                    @Parameter(
                            name = "name",
                            description = "Строка для поиска в имени",
                            required = true,
                            in = ParameterIn.QUERY)
                    @Valid
                    @RequestParam(value = "name", required = true)
                    String name);

    /**
     * PUT /api/v1/templates/{id} : Обновить запись
     * Обновляет существующую запись по указанному ID
     *
     * @param id ID записи для обновления (required)
     * @param springAppTemplateDto (required)
     * @return OK (status code 200)
     */
    @Operation(
            operationId = "update",
            summary = "Обновить запись",
            description = "Обновляет существующую запись по указанному ID",
            tags = {"Spring App Template Controller"},
            responses = {
                @ApiResponse(
                        responseCode = "200",
                        description = "OK",
                        content = {
                            @Content(mediaType = "*/*", schema = @Schema(implementation = SpringAppTemplateDto.class))
                        })
            })
    @RequestMapping(
            method = RequestMethod.PUT,
            value = "/api/v1/templates/{id}",
            produces = {"*/*"},
            consumes = {"application/json"})
    ResponseEntity<SpringAppTemplateDto> update(
            @Parameter(name = "id", description = "ID записи для обновления", required = true, in = ParameterIn.PATH)
                    @PathVariable("id")
                    UUID id,
            @Parameter(name = "SpringAppTemplateDto", description = "", required = true) @Valid @RequestBody
                    SpringAppTemplateDto springAppTemplateDto);
}
