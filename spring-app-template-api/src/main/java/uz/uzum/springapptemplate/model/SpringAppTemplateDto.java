package uz.uzum.springapptemplate.model;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Generated;
import java.util.Objects;
import java.util.UUID;
import org.springframework.lang.Nullable;

/**
 * Данные для создания записи
 */
@lombok.Setter
@lombok.Getter
@lombok.Builder
@lombok.NoArgsConstructor
@lombok.AllArgsConstructor
@Schema(name = "SpringAppTemplateDto", description = "Данные для создания записи")
@Generated(value = "org.openapitools.codegen.languages.SpringCodegen", comments = "Generator version: 7.12.0")
public class SpringAppTemplateDto {

    private @Nullable UUID id;

    private String name;

    private @Nullable String description;

    private Boolean isActive;

    private @Nullable Integer priority;

    /**
     * Constructor with only required parameters
     */
    public SpringAppTemplateDto(String name, Boolean isActive) {
        this.name = name;
        this.isActive = isActive;
    }

    public SpringAppTemplateDto id(UUID id) {
        this.id = id;
        return this;
    }

    public SpringAppTemplateDto name(String name) {
        this.name = name;
        return this;
    }

    public SpringAppTemplateDto description(String description) {
        this.description = description;
        return this;
    }

    public SpringAppTemplateDto isActive(Boolean isActive) {
        this.isActive = isActive;
        return this;
    }

    public SpringAppTemplateDto priority(Integer priority) {
        this.priority = priority;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        SpringAppTemplateDto springAppTemplateDto = (SpringAppTemplateDto) o;
        return Objects.equals(this.id, springAppTemplateDto.id)
                && Objects.equals(this.name, springAppTemplateDto.name)
                && Objects.equals(this.description, springAppTemplateDto.description)
                && Objects.equals(this.isActive, springAppTemplateDto.isActive)
                && Objects.equals(this.priority, springAppTemplateDto.priority);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, name, description, isActive, priority);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class SpringAppTemplateDto {\n");
        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("    name: ").append(toIndentedString(name)).append("\n");
        sb.append("    description: ").append(toIndentedString(description)).append("\n");
        sb.append("    isActive: ").append(toIndentedString(isActive)).append("\n");
        sb.append("    priority: ").append(toIndentedString(priority)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }
}
