---
services:
  prometheus:
    image: "prom/prometheus:latest"
    container_name: "spring-app-template-prometheus"
    ports:
      - "9090:9090"
    command:
      - "--config.file=/etc/prometheus/prometheus.yml"
    volumes:
      - "./config/prometheus.yaml:/etc/prometheus/prometheus.yml:ro"
    extra_hosts:
      - "host.docker.internal:host-gateway"
    networks:
      spring-app-template: null
  grafana:
    image: "grafana/grafana:latest"
    container_name: "spring-app-template-grafana"
    depends_on:
      - "prometheus"
    volumes:
      - "./config/grafana/datasources:/etc/grafana/provisioning/datasources/"
      - "./config/grafana/dashboards:/etc/grafana/provisioning/dashboards/"
    ports:
      - "3000:3000"
    environment:
      - "GF_SECURITY_ADMIN_USER=admin"
      - "GF_SECURITY_ADMIN_PASSWORD=admin"
    networks:
      spring-app-template: null
  postgres:
    image: "postgres:latest"
    container_name: "spring-app-template-postgres"
    restart: "always"
    environment:
      - "POSTGRES_USER=postgres"
      - "POSTGRES_PASSWORD=postgres"
    volumes:
      - "pg-data:/var/lib/postgresql/data"
    ports:
      - "54322:5432"
    networks:
      spring-app-template: null
  sentry:
    image: "wiremock/wiremock:latest"
    container_name: "spring-app-template-sentry"
    ports:
      - "10100:8080"
    volumes:
      - "./infrastructure/external-services/spring-app-template-sentry/:/home/<USER>/"
    command: "--disable-banner --disable-gzip"
volumes:
  pg-data:
    driver: "local"
networks:
  spring-app-template: null
