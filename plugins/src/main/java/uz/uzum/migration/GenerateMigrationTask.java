package uz.uzum.migration;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.AbstractMap.SimpleEntry;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.gradle.api.DefaultTask;
import org.gradle.api.GradleException;
import org.gradle.api.provider.Property;
import org.gradle.api.tasks.Input;
import org.gradle.api.tasks.TaskAction;
import org.yaml.snakeyaml.Yaml;

@Slf4j
public abstract class GenerateMigrationTask extends DefaultTask {

    @Input
    public abstract Property<String> getMigrationName();

    public GenerateMigrationTask() {
        getMigrationName().convention("");
    }

    @TaskAction
    public void generate() {
        validateInput();
        SimpleEntry<File, File> result = createMigrationFile();
        File yearMonthDir = result.getKey();
        File migrationFile = result.getValue();
        File masterChangelog = updateMasterChangelog(yearMonthDir);
        writeTemplateToFile(migrationFile);
        commitToGit(migrationFile, masterChangelog);
    }

    private void validateInput() {
        if (getMigrationName().get().isBlank()) {
            throw new GradleException("Please specify -PmigrationName=your_migration_name");
        }
    }

    private SimpleEntry<File, File> createMigrationFile() {
        LocalDateTime timestamp = LocalDateTime.now();
        String yearMonth = timestamp.format(DateTimeFormatter.ofPattern("yyyy-MM"));

        File changelogDir = getProject().file("spring-app-template-impl/src/main/resources/db/changelog");
        File yearMonthDir = new File(changelogDir, yearMonth);
        yearMonthDir.mkdirs();

        String formattedTimestamp = timestamp.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String migrationFileName = formattedTimestamp + "_" + getMigrationName().get();
        File migrationFile = new File(yearMonthDir, migrationFileName + ".yml");
        try {
            migrationFile.createNewFile();
        } catch (IOException e) {
            throw new GradleException("Failed to create migration file", e);
        }

        return new SimpleEntry<>(yearMonthDir, migrationFile);
    }

    private void writeTemplateToFile(File migrationFile) {
        String gitUsername = getGitUsername();
        String migrationFileName =
                migrationFile.getName().substring(0, migrationFile.getName().lastIndexOf('.'));

        try {
            java.nio.file.Files.writeString(migrationFile.toPath(), generateTemplate(migrationFileName, gitUsername));
        } catch (IOException e) {
            throw new GradleException("Failed to write template to file", e);
        }
    }

    private File updateMasterChangelog(File yearMonthDir) {
        File changelogDir = yearMonthDir.getParentFile();
        File masterChangelog = new File(changelogDir, "db.changelog-master.yml");
        String yearMonth = yearMonthDir.getName();

        if (!masterChangelog.exists()) {
            String initialContent = "databaseChangeLog:\n" + "  - includeAll:\n"
                    + "      path: "
                    + yearMonth + "\n" + "      relativeToChangelogFile: true\n";
            try {
                java.nio.file.Files.writeString(masterChangelog.toPath(), initialContent);
                return masterChangelog;
            } catch (IOException e) {
                throw new GradleException("Failed to create master changelog", e);
            }
        }

        String content;
        try {
            content = java.nio.file.Files.readString(masterChangelog.toPath());
        } catch (IOException e) {
            throw new GradleException("Failed to read master changelog", e);
        }

        if (hasExistingIncludeAll(content, yearMonth)) {
            log.info("Directory {} is already included in master changelog, skipping update", yearMonth);
            return masterChangelog;
        }

        Yaml yamlMapper = new Yaml();
        Map<String, Object> data = yamlMapper.load(content);

        @SuppressWarnings("unchecked")
        List<Map<String, Object>> changeLog = (List<Map<String, Object>>) data.get("databaseChangeLog");

        Map<String, Object> includeAllMap = new HashMap<>();
        includeAllMap.put("path", yearMonth);
        includeAllMap.put("relativeToChangelogFile", true);

        Map<String, Object> entry = new HashMap<>();
        entry.put("includeAll", includeAllMap);

        changeLog.add(entry);

        try {
            java.nio.file.Files.writeString(
                    masterChangelog.toPath(), yamlMapper.dump(Map.of("databaseChangeLog", changeLog)));
        } catch (IOException e) {
            throw new GradleException("Failed to update master changelog", e);
        }

        return masterChangelog;
    }

    private boolean hasExistingIncludeAll(String content, String yearMonth) {
        Yaml yamlMapper = new Yaml();
        Map<String, Object> data = yamlMapper.load(content);

        @SuppressWarnings("unchecked")
        List<Map<String, Object>> changeLog = (List<Map<String, Object>>) data.get("databaseChangeLog");

        for (Map<String, Object> change : changeLog) {
            @SuppressWarnings("unchecked")
            Map<String, Object> includeAll = (Map<String, Object>) change.get("includeAll");
            if (includeAll != null && yearMonth.equals(includeAll.get("path"))) {
                return true;
            }
        }
        return false;
    }

    private void commitToGit(File migrationFile, File masterChangelog) {
        log.info("Created migration file: {}", migrationFile.getAbsolutePath());
        log.info("Updated master changelog: {}", masterChangelog.getAbsolutePath());

        gitAdd(migrationFile);
        gitAdd(masterChangelog);
    }

    private String getGitUsername() {
        try {
            ProcessBuilder processBuilder = new ProcessBuilder("git", "config", "user.name")
                    .redirectOutput(ProcessBuilder.Redirect.PIPE)
                    .redirectError(ProcessBuilder.Redirect.PIPE);

            Process process = processBuilder.start();
            process.waitFor();

            BufferedReader reader = new BufferedReader(new java.io.InputStreamReader(process.getInputStream()));
            String username = reader.readLine();

            if (username != null && !username.trim().isEmpty()) {
                return username.trim();
            } else {
                return System.getProperty("user.name");
            }
        } catch (Exception e) {
            return System.getProperty("user.name");
        }
    }

    private void gitAdd(File file) {
        try {
            ProcessBuilder processBuilder = new ProcessBuilder("git", "add", file.getAbsolutePath())
                    .redirectOutput(ProcessBuilder.Redirect.PIPE)
                    .redirectError(ProcessBuilder.Redirect.PIPE);

            Process process = processBuilder.start();
            int exitCode = process.waitFor();

            if (exitCode == 0) {
                log.info("Added {} to git staging area", file.getName());
            } else {
                BufferedReader errorReader =
                        new BufferedReader(new java.io.InputStreamReader(process.getErrorStream()));
                String errorOutput = errorReader.readLine();
                log.warn("Failed to add {} to git: {}", file.getName(), errorOutput);
            }
        } catch (Exception e) {
            log.warn("Failed to execute git add command: {}", e.getMessage());
        }
    }

    private String generateTemplate(String migrationFileName, String gitUsername) {
        return "databaseChangeLog:\n" + "    - changeSet:\n"
                + "        id: "
                + migrationFileName + "\n" + "        author: "
                + gitUsername + "\n" + "\n"
                + "        preConditions:\n"
                + "        - onFail: MARK_RAN\n"
                + "        \n"
                + "    # changes:\n"
                + "        # Template for table creation\n"
                + "        # - createTable:\n"
                + "        #     tableName: your_table\n"
                + "        #     columns:\n"
                + "        #       - column:\n"
                + "        #           name: id\n"
                + "        #           type: bigint\n"
                + "        #           autoIncrement: true\n"
                + "        #           constraints:\n"
                + "        #             primaryKey: true\n"
                + "        #             nullable: false";
    }
}
