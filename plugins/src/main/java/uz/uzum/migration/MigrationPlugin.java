package uz.uzum.migration;

import org.gradle.api.Plugin;
import org.gradle.api.Project;

@SuppressWarnings("unused")
public final class MigrationPlugin implements Plugin<Project> {
    @Override
    public void apply(Project project) {
        project.getTasks().register("generateMigration", GenerateMigrationTask.class, task -> {
            task.setGroup("database");
            task.setDescription("Generates a new Liquibase migration file");

            task.getMigrationName().convention(project.provider(() -> {
                Object migrationNameProperty = project.findProperty("migrationName");
                return migrationNameProperty != null ? migrationNameProperty.toString() : "";
            }));
        });
    }
}
