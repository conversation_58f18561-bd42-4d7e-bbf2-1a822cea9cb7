@file:Suppress("UnstableApiUsage")

dependencyResolutionManagement {

    repositories {
        mavenCentral()
        gradlePluginPortal()
    }

    versionCatalogs {
        create("libs") {
            version("snakeyaml", "2.0")
            version("lombok", "1.18.30")

            library("snakeyaml", "org.yaml", "snakeyaml").versionRef("snakeyaml")
            library("lombok", "org.projectlombok", "lombok").versionRef("lombok")
        }
    }
}
